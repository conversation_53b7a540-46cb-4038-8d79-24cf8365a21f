﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Zify.Settlement.Application.Infrastructure.Configurations;

public sealed class ServiceDiscoveryOptions
{
    public string InquiryServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_UserServices_Address")]
    public string UserServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_FileManager_Address")]
    public string FileManagerServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_FileManager_Grpc_Address")]
    public string FileManagerGrpcServiceAddress { get; set; }

    [ConfigurationKeyName("Files_Base_Url")]
    public string S3ServiceAddress { get; set; }

    public string PaymentServiceAddress { get; set; }

    public string PaymentGrpcServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_Ipg_Address")]
    public string IpgServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_Integrations_Address")]
    public string IntegrationServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_Inquiry_ApiGateway_Grpc_Address")]
    public string InquiryGrpcServiceAddress { get; set; }

    [ConfigurationKeyName("Refund_Grpc_Address")]
    public string RefundGrpcServiceAddress { get; set; }

    [ConfigurationKeyName("PayPing_Token_Grpc_Address")]
    public string TokenGrpcServiceAddress { get; set; }

    [ConfigurationKeyName("Wallet_Grpc_Address")]
    public string WalletGrpcAddress { get; set; }
}